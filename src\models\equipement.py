from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class Secteur(db.Model):
    __tablename__ = 'secteurs'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False, unique=True)
    numero = db.Column(db.Integer, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    equipements = db.relationship('Equipement', backref='secteur', lazy=True)
    
    def __repr__(self):
        return f'<Secteur {self.nom}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'nom': self.nom,
            'numero': self.numero,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Equipement(db.Model):
    __tablename__ = 'equipements'
    
    id = db.Column(db.Integer, primary_key=True)
    secteur_id = db.Column(db.Integer, db.<PERSON>ey('secteurs.id'), nullable=True)
    nom = db.Column(db.String(200), nullable=False)
    symbole = db.Column(db.String(10))
    code_equipement = db.Column(db.String(50), unique=True)
    budget = db.Column(db.Numeric(15, 2))
    fabricant = db.Column(db.String(200))
    fournisseur = db.Column(db.String(200))
    fourniture_externe = db.Column(db.Text)
    fourniture_interne = db.Column(db.Text)
    statut = db.Column(db.String(50), default='Actif')
    date_installation = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    pieces_rechange = db.relationship('PieceRechange', backref='equipement', lazy=True, cascade='all, delete-orphan')
    interventions = db.relationship('Intervention', backref='equipement', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Equipement {self.nom}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'secteur_id': self.secteur_id,
            'nom': self.nom,
            'symbole': self.symbole,
            'code_equipement': self.code_equipement,
            'budget': float(self.budget) if self.budget else None,
            'fabricant': self.fabricant,
            'fournisseur': self.fournisseur,
            'fourniture_externe': self.fourniture_externe,
            'fourniture_interne': self.fourniture_interne,
            'statut': self.statut,
            'date_installation': self.date_installation.isoformat() if self.date_installation else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'secteur_nom': self.secteur.nom if self.secteur else None
        }

class PieceRechange(db.Model):
    __tablename__ = 'pieces_rechange'
    
    id = db.Column(db.Integer, primary_key=True)
    equipement_id = db.Column(db.Integer, db.ForeignKey('equipements.id'), nullable=False)
    nom = db.Column(db.String(200), nullable=False)
    reference = db.Column(db.String(100))
    description = db.Column(db.Text)
    quantite_stock = db.Column(db.Integer, default=0)
    quantite_min = db.Column(db.Integer, default=0)
    prix_unitaire = db.Column(db.Numeric(10, 2))
    fournisseur = db.Column(db.String(200))
    delai_livraison = db.Column(db.Integer)  # en jours
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    pieces_utilisees = db.relationship('PieceUtilisee', backref='piece', lazy=True)
    
    def __repr__(self):
        return f'<PieceRechange {self.nom}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'equipement_id': self.equipement_id,
            'nom': self.nom,
            'reference': self.reference,
            'description': self.description,
            'quantite_stock': self.quantite_stock,
            'quantite_min': self.quantite_min,
            'prix_unitaire': float(self.prix_unitaire) if self.prix_unitaire else None,
            'fournisseur': self.fournisseur,
            'delai_livraison': self.delai_livraison,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'equipement_nom': self.equipement.nom if self.equipement else None,
            'stock_critique': self.quantite_stock <= self.quantite_min
        }

class Intervention(db.Model):
    __tablename__ = 'interventions'
    
    id = db.Column(db.Integer, primary_key=True)
    equipement_id = db.Column(db.Integer, db.ForeignKey('equipements.id'), nullable=False)
    type_intervention = db.Column(db.String(50), nullable=False)  # 'Préventive', 'Corrective', 'Urgente'
    titre = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    technicien = db.Column(db.String(100))
    date_prevue = db.Column(db.Date)
    date_debut = db.Column(db.DateTime)
    date_fin = db.Column(db.DateTime)
    statut = db.Column(db.String(50), default='Planifiée')  # 'Planifiée', 'En cours', 'Terminée', 'Annulée'
    cout = db.Column(db.Numeric(10, 2))
    observations = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    pieces_utilisees = db.relationship('PieceUtilisee', backref='intervention', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Intervention {self.titre}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'equipement_id': self.equipement_id,
            'type_intervention': self.type_intervention,
            'titre': self.titre,
            'description': self.description,
            'technicien': self.technicien,
            'date_prevue': self.date_prevue.isoformat() if self.date_prevue else None,
            'date_debut': self.date_debut.isoformat() if self.date_debut else None,
            'date_fin': self.date_fin.isoformat() if self.date_fin else None,
            'statut': self.statut,
            'cout': float(self.cout) if self.cout else None,
            'observations': self.observations,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'equipement_nom': self.equipement.nom if self.equipement else None,
            'duree': self.get_duree()
        }
    
    def get_duree(self):
        """Calcule la durée de l'intervention en heures"""
        if self.date_debut and self.date_fin:
            delta = self.date_fin - self.date_debut
            return round(delta.total_seconds() / 3600, 2)
        return None

class PieceUtilisee(db.Model):
    __tablename__ = 'pieces_utilisees'
    
    id = db.Column(db.Integer, primary_key=True)
    intervention_id = db.Column(db.Integer, db.ForeignKey('interventions.id'), nullable=False)
    piece_id = db.Column(db.Integer, db.ForeignKey('pieces_rechange.id'), nullable=False)
    quantite_utilisee = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<PieceUtilisee {self.quantite_utilisee}x {self.piece.nom}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'intervention_id': self.intervention_id,
            'piece_id': self.piece_id,
            'quantite_utilisee': self.quantite_utilisee,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'piece_nom': self.piece.nom if self.piece else None,
            'piece_reference': self.piece.reference if self.piece else None,
            'cout_total': float(self.piece.prix_unitaire * self.quantite_utilisee) if self.piece and self.piece.prix_unitaire else None
        }

class Historique(db.Model):
    __tablename__ = 'historique'
    
    id = db.Column(db.Integer, primary_key=True)
    table_name = db.Column(db.String(50), nullable=False)
    record_id = db.Column(db.Integer, nullable=False)
    action = db.Column(db.String(20), nullable=False)  # 'CREATE', 'UPDATE', 'DELETE'
    old_values = db.Column(db.Text)  # JSON des anciennes valeurs
    new_values = db.Column(db.Text)  # JSON des nouvelles valeurs
    user_name = db.Column(db.String(100))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Historique {self.action} {self.table_name}:{self.record_id}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'action': self.action,
            'old_values': json.loads(self.old_values) if self.old_values else None,
            'new_values': json.loads(self.new_values) if self.new_values else None,
            'user_name': self.user_name,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }
    
    @staticmethod
    def log_action(table_name, record_id, action, old_values=None, new_values=None, user_name=None):
        """Enregistre une action dans l'historique"""
        historique = Historique(
            table_name=table_name,
            record_id=record_id,
            action=action,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None,
            user_name=user_name
        )
        db.session.add(historique)
        db.session.commit()

