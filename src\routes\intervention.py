from flask import Blueprint, request, jsonify
from datetime import datetime, date
from src.models.equipement import db, Intervention, Equipement, PieceRechange, PieceUtilisee, Historique

intervention_bp = Blueprint('intervention', __name__)

@intervention_bp.route('/interventions', methods=['GET'])
def get_interventions():
    """Récupère toutes les interventions avec filtres optionnels"""
    try:
        query = Intervention.query
        
        # Filtres optionnels
        equipement_id = request.args.get('equipement_id', type=int)
        type_intervention = request.args.get('type_intervention')
        statut = request.args.get('statut')
        technicien = request.args.get('technicien')
        date_debut = request.args.get('date_debut')
        date_fin = request.args.get('date_fin')
        
        if equipement_id:
            query = query.filter(Intervention.equipement_id == equipement_id)
        
        if type_intervention:
            query = query.filter(Intervention.type_intervention == type_intervention)
        
        if statut:
            query = query.filter(Intervention.statut == statut)
        
        if technicien:
            query = query.filter(Intervention.technicien.contains(technicien))
        
        if date_debut:
            query = query.filter(Intervention.date_prevue >= datetime.strptime(date_debut, '%Y-%m-%d').date())
        
        if date_fin:
            query = query.filter(Intervention.date_prevue <= datetime.strptime(date_fin, '%Y-%m-%d').date())
        
        # Trier par date prévue décroissante
        interventions = query.order_by(Intervention.date_prevue.desc()).all()
        return jsonify([intervention.to_dict() for intervention in interventions])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/<int:intervention_id>', methods=['GET'])
def get_intervention(intervention_id):
    """Récupère une intervention par son ID avec les pièces utilisées"""
    try:
        intervention = Intervention.query.get_or_404(intervention_id)
        result = intervention.to_dict()
        
        # Ajouter les pièces utilisées
        pieces_utilisees = []
        for piece_utilisee in intervention.pieces_utilisees:
            pieces_utilisees.append(piece_utilisee.to_dict())
        
        result['pieces_utilisees'] = pieces_utilisees
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions', methods=['POST'])
def create_intervention():
    """Crée une nouvelle intervention"""
    try:
        data = request.get_json()
        
        # Validation des données requises
        if not data.get('titre'):
            return jsonify({'error': 'Le titre de l\'intervention est requis'}), 400
        
        if not data.get('equipement_id'):
            return jsonify({'error': 'L\'ID de l\'équipement est requis'}), 400
        
        if not data.get('type_intervention'):
            return jsonify({'error': 'Le type d\'intervention est requis'}), 400
        
        # Vérifier que l'équipement existe
        equipement = Equipement.query.get(data['equipement_id'])
        if not equipement:
            return jsonify({'error': 'Équipement non trouvé'}), 400
        
        intervention = Intervention(
            equipement_id=data['equipement_id'],
            type_intervention=data['type_intervention'],
            titre=data['titre'],
            description=data.get('description'),
            technicien=data.get('technicien'),
            date_prevue=datetime.strptime(data['date_prevue'], '%Y-%m-%d').date() if data.get('date_prevue') else None,
            statut=data.get('statut', 'Planifiée'),
            cout=data.get('cout'),
            observations=data.get('observations')
        )
        
        db.session.add(intervention)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('interventions', intervention.id, 'CREATE', new_values=intervention.to_dict())
        
        return jsonify(intervention.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/<int:intervention_id>', methods=['PUT'])
def update_intervention(intervention_id):
    """Met à jour une intervention"""
    try:
        intervention = Intervention.query.get_or_404(intervention_id)
        data = request.get_json()
        
        # Sauvegarder les anciennes valeurs pour l'historique
        old_values = intervention.to_dict()
        
        # Vérifier que l'équipement existe si modifié
        if data.get('equipement_id') and data['equipement_id'] != intervention.equipement_id:
            equipement = Equipement.query.get(data['equipement_id'])
            if not equipement:
                return jsonify({'error': 'Équipement non trouvé'}), 400
            intervention.equipement_id = data['equipement_id']
        
        # Mettre à jour les autres champs
        if 'type_intervention' in data:
            intervention.type_intervention = data['type_intervention']
        if 'titre' in data:
            intervention.titre = data['titre']
        if 'description' in data:
            intervention.description = data['description']
        if 'technicien' in data:
            intervention.technicien = data['technicien']
        if 'date_prevue' in data:
            intervention.date_prevue = datetime.strptime(data['date_prevue'], '%Y-%m-%d').date() if data['date_prevue'] else None
        if 'date_debut' in data:
            intervention.date_debut = datetime.strptime(data['date_debut'], '%Y-%m-%d %H:%M:%S') if data['date_debut'] else None
        if 'date_fin' in data:
            intervention.date_fin = datetime.strptime(data['date_fin'], '%Y-%m-%d %H:%M:%S') if data['date_fin'] else None
        if 'statut' in data:
            intervention.statut = data['statut']
        if 'cout' in data:
            intervention.cout = data['cout']
        if 'observations' in data:
            intervention.observations = data['observations']
        
        intervention.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('interventions', intervention.id, 'UPDATE', old_values=old_values, new_values=intervention.to_dict())
        
        return jsonify(intervention.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/<int:intervention_id>', methods=['DELETE'])
def delete_intervention(intervention_id):
    """Supprime une intervention"""
    try:
        intervention = Intervention.query.get_or_404(intervention_id)
        
        # Sauvegarder les valeurs pour l'historique
        old_values = intervention.to_dict()
        
        db.session.delete(intervention)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('interventions', intervention_id, 'DELETE', old_values=old_values)
        
        return jsonify({'message': 'Intervention supprimée avec succès'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/<int:intervention_id>/pieces', methods=['POST'])
def add_piece_to_intervention(intervention_id):
    """Ajoute une pièce utilisée à une intervention"""
    try:
        intervention = Intervention.query.get_or_404(intervention_id)
        data = request.get_json()
        
        if not data.get('piece_id'):
            return jsonify({'error': 'L\'ID de la pièce est requis'}), 400
        
        if not data.get('quantite_utilisee'):
            return jsonify({'error': 'La quantité utilisée est requise'}), 400
        
        # Vérifier que la pièce existe
        piece = PieceRechange.query.get(data['piece_id'])
        if not piece:
            return jsonify({'error': 'Pièce non trouvée'}), 400
        
        # Vérifier qu'il y a assez de stock
        if piece.quantite_stock < data['quantite_utilisee']:
            return jsonify({'error': 'Stock insuffisant'}), 400
        
        # Vérifier que la pièce n'est pas déjà utilisée dans cette intervention
        existing = PieceUtilisee.query.filter_by(
            intervention_id=intervention_id,
            piece_id=data['piece_id']
        ).first()
        
        if existing:
            return jsonify({'error': 'Cette pièce est déjà utilisée dans cette intervention'}), 400
        
        piece_utilisee = PieceUtilisee(
            intervention_id=intervention_id,
            piece_id=data['piece_id'],
            quantite_utilisee=data['quantite_utilisee']
        )
        
        # Décrémenter le stock
        piece.quantite_stock -= data['quantite_utilisee']
        
        db.session.add(piece_utilisee)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('pieces_utilisees', piece_utilisee.id, 'CREATE', new_values=piece_utilisee.to_dict())
        
        return jsonify(piece_utilisee.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/<int:intervention_id>/pieces/<int:piece_utilisee_id>', methods=['DELETE'])
def remove_piece_from_intervention(intervention_id, piece_utilisee_id):
    """Retire une pièce utilisée d'une intervention"""
    try:
        piece_utilisee = PieceUtilisee.query.filter_by(
            id=piece_utilisee_id,
            intervention_id=intervention_id
        ).first_or_404()
        
        # Remettre le stock
        piece = piece_utilisee.piece
        piece.quantite_stock += piece_utilisee.quantite_utilisee
        
        # Sauvegarder les valeurs pour l'historique
        old_values = piece_utilisee.to_dict()
        
        db.session.delete(piece_utilisee)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('pieces_utilisees', piece_utilisee_id, 'DELETE', old_values=old_values)
        
        return jsonify({'message': 'Pièce retirée de l\'intervention avec succès'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/stats', methods=['GET'])
def get_interventions_stats():
    """Récupère les statistiques des interventions"""
    try:
        total = Intervention.query.count()
        planifiees = Intervention.query.filter_by(statut='Planifiée').count()
        en_cours = Intervention.query.filter_by(statut='En cours').count()
        terminees = Intervention.query.filter_by(statut='Terminée').count()
        
        # Interventions par type
        preventives = Intervention.query.filter_by(type_intervention='Préventive').count()
        correctives = Intervention.query.filter_by(type_intervention='Corrective').count()
        urgentes = Intervention.query.filter_by(type_intervention='Urgente').count()
        
        # Coût total des interventions
        cout_total = db.session.query(db.func.sum(Intervention.cout)).scalar() or 0
        
        return jsonify({
            'total': total,
            'planifiees': planifiees,
            'en_cours': en_cours,
            'terminees': terminees,
            'preventives': preventives,
            'correctives': correctives,
            'urgentes': urgentes,
            'cout_total': float(cout_total)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@intervention_bp.route('/interventions/planning', methods=['GET'])
def get_planning():
    """Récupère le planning des interventions à venir"""
    try:
        today = date.today()
        interventions = Intervention.query.filter(
            Intervention.date_prevue >= today,
            Intervention.statut.in_(['Planifiée', 'En cours'])
        ).order_by(Intervention.date_prevue).all()
        
        return jsonify([intervention.to_dict() for intervention in interventions])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

