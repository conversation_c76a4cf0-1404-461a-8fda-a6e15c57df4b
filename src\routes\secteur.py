from flask import Blueprint, request, jsonify
from src.models.equipement import db, Secteur, Historique

secteur_bp = Blueprint('secteur', __name__)

@secteur_bp.route('/secteurs', methods=['GET'])
def get_secteurs():
    """Récupère tous les secteurs"""
    try:
        secteurs = Secteur.query.all()
        return jsonify([secteur.to_dict() for secteur in secteurs])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@secteur_bp.route('/secteurs/<int:secteur_id>', methods=['GET'])
def get_secteur(secteur_id):
    """Récupère un secteur par son ID"""
    try:
        secteur = Secteur.query.get_or_404(secteur_id)
        return jsonify(secteur.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@secteur_bp.route('/secteurs', methods=['POST'])
def create_secteur():
    """Crée un nouveau secteur"""
    try:
        data = request.get_json()
        
        # Validation des données requises
        if not data.get('nom'):
            return jsonify({'error': 'Le nom du secteur est requis'}), 400
        
        # Vérifier l'unicité du nom
        if Secteur.query.filter_by(nom=data['nom']).first():
            return jsonify({'error': 'Un secteur avec ce nom existe déjà'}), 400
        
        # Vérifier l'unicité du numéro si fourni
        if data.get('numero') and Secteur.query.filter_by(numero=data['numero']).first():
            return jsonify({'error': 'Un secteur avec ce numéro existe déjà'}), 400
        
        secteur = Secteur(
            nom=data['nom'],
            numero=data.get('numero'),
            description=data.get('description')
        )
        
        db.session.add(secteur)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('secteurs', secteur.id, 'CREATE', new_values=secteur.to_dict())
        
        return jsonify(secteur.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@secteur_bp.route('/secteurs/<int:secteur_id>', methods=['PUT'])
def update_secteur(secteur_id):
    """Met à jour un secteur"""
    try:
        secteur = Secteur.query.get_or_404(secteur_id)
        data = request.get_json()
        
        # Sauvegarder les anciennes valeurs pour l'historique
        old_values = secteur.to_dict()
        
        # Vérifier l'unicité du nom si modifié
        if data.get('nom') and data['nom'] != secteur.nom:
            if Secteur.query.filter_by(nom=data['nom']).first():
                return jsonify({'error': 'Un secteur avec ce nom existe déjà'}), 400
            secteur.nom = data['nom']
        
        # Vérifier l'unicité du numéro si modifié
        if data.get('numero') and data['numero'] != secteur.numero:
            if Secteur.query.filter_by(numero=data['numero']).first():
                return jsonify({'error': 'Un secteur avec ce numéro existe déjà'}), 400
            secteur.numero = data['numero']
        
        if 'description' in data:
            secteur.description = data['description']
        
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('secteurs', secteur.id, 'UPDATE', old_values=old_values, new_values=secteur.to_dict())
        
        return jsonify(secteur.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@secteur_bp.route('/secteurs/<int:secteur_id>', methods=['DELETE'])
def delete_secteur(secteur_id):
    """Supprime un secteur"""
    try:
        secteur = Secteur.query.get_or_404(secteur_id)
        
        # Vérifier s'il y a des équipements associés
        if secteur.equipements:
            return jsonify({'error': 'Impossible de supprimer un secteur qui contient des équipements'}), 400
        
        # Sauvegarder les valeurs pour l'historique
        old_values = secteur.to_dict()
        
        db.session.delete(secteur)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('secteurs', secteur_id, 'DELETE', old_values=old_values)
        
        return jsonify({'message': 'Secteur supprimé avec succès'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

