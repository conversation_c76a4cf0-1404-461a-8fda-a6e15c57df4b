from flask import Blueprint, request, jsonify
from datetime import datetime
from src.models.equipement import db, Equipement, Secteur, Historique

equipement_bp = Blueprint('equipement', __name__)

@equipement_bp.route('/equipements', methods=['GET'])
def get_equipements():
    """Récupère tous les équipements avec filtres optionnels"""
    try:
        query = Equipement.query
        
        # Filtres optionnels
        secteur_id = request.args.get('secteur_id', type=int)
        statut = request.args.get('statut')
        search = request.args.get('search')
        
        if secteur_id:
            query = query.filter(Equipement.secteur_id == secteur_id)
        
        if statut:
            query = query.filter(Equipement.statut == statut)
        
        if search:
            query = query.filter(
                db.or_(
                    Equipement.nom.contains(search),
                    Equipement.code_equipement.contains(search),
                    Equipement.fabricant.contains(search)
                )
            )
        
        equipements = query.all()
        return jsonify([equipement.to_dict() for equipement in equipements])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@equipement_bp.route('/equipements/<int:equipement_id>', methods=['GET'])
def get_equipement(equipement_id):
    """Récupère un équipement par son ID"""
    try:
        equipement = Equipement.query.get_or_404(equipement_id)
        return jsonify(equipement.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@equipement_bp.route('/equipements', methods=['POST'])
def create_equipement():
    """Crée un nouvel équipement"""
    try:
        data = request.get_json()
        
        # Validation des données requises
        if not data.get('nom'):
            return jsonify({'error': 'Le nom de l\'équipement est requis'}), 400
        
        # Vérifier l'unicité du code équipement si fourni
        if data.get('code_equipement'):
            if Equipement.query.filter_by(code_equipement=data['code_equipement']).first():
                return jsonify({'error': 'Un équipement avec ce code existe déjà'}), 400
        
        # Vérifier que le secteur existe si fourni
        if data.get('secteur_id'):
            secteur = Secteur.query.get(data['secteur_id'])
            if not secteur:
                return jsonify({'error': 'Secteur non trouvé'}), 400
        
        equipement = Equipement(
            secteur_id=data.get('secteur_id'),
            nom=data['nom'],
            symbole=data.get('symbole'),
            code_equipement=data.get('code_equipement'),
            budget=data.get('budget'),
            fabricant=data.get('fabricant'),
            fournisseur=data.get('fournisseur'),
            fourniture_externe=data.get('fourniture_externe'),
            fourniture_interne=data.get('fourniture_interne'),
            statut=data.get('statut', 'Actif'),
            date_installation=datetime.strptime(data['date_installation'], '%Y-%m-%d').date() if data.get('date_installation') else None
        )
        
        db.session.add(equipement)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('equipements', equipement.id, 'CREATE', new_values=equipement.to_dict())
        
        return jsonify(equipement.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@equipement_bp.route('/equipements/<int:equipement_id>', methods=['PUT'])
def update_equipement(equipement_id):
    """Met à jour un équipement"""
    try:
        equipement = Equipement.query.get_or_404(equipement_id)
        data = request.get_json()
        
        # Sauvegarder les anciennes valeurs pour l'historique
        old_values = equipement.to_dict()
        
        # Vérifier l'unicité du code équipement si modifié
        if data.get('code_equipement') and data['code_equipement'] != equipement.code_equipement:
            if Equipement.query.filter_by(code_equipement=data['code_equipement']).first():
                return jsonify({'error': 'Un équipement avec ce code existe déjà'}), 400
            equipement.code_equipement = data['code_equipement']
        
        # Vérifier que le secteur existe si modifié
        if data.get('secteur_id') and data['secteur_id'] != equipement.secteur_id:
            secteur = Secteur.query.get(data['secteur_id'])
            if not secteur:
                return jsonify({'error': 'Secteur non trouvé'}), 400
            equipement.secteur_id = data['secteur_id']
        
        # Mettre à jour les autres champs
        if 'nom' in data:
            equipement.nom = data['nom']
        if 'symbole' in data:
            equipement.symbole = data['symbole']
        if 'budget' in data:
            equipement.budget = data['budget']
        if 'fabricant' in data:
            equipement.fabricant = data['fabricant']
        if 'fournisseur' in data:
            equipement.fournisseur = data['fournisseur']
        if 'fourniture_externe' in data:
            equipement.fourniture_externe = data['fourniture_externe']
        if 'fourniture_interne' in data:
            equipement.fourniture_interne = data['fourniture_interne']
        if 'statut' in data:
            equipement.statut = data['statut']
        if 'date_installation' in data:
            equipement.date_installation = datetime.strptime(data['date_installation'], '%Y-%m-%d').date() if data['date_installation'] else None
        
        equipement.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('equipements', equipement.id, 'UPDATE', old_values=old_values, new_values=equipement.to_dict())
        
        return jsonify(equipement.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@equipement_bp.route('/equipements/<int:equipement_id>', methods=['DELETE'])
def delete_equipement(equipement_id):
    """Supprime un équipement"""
    try:
        equipement = Equipement.query.get_or_404(equipement_id)
        
        # Sauvegarder les valeurs pour l'historique
        old_values = equipement.to_dict()
        
        db.session.delete(equipement)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('equipements', equipement_id, 'DELETE', old_values=old_values)
        
        return jsonify({'message': 'Équipement supprimé avec succès'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@equipement_bp.route('/equipements/stats', methods=['GET'])
def get_equipements_stats():
    """Récupère les statistiques des équipements"""
    try:
        total = Equipement.query.count()
        actifs = Equipement.query.filter_by(statut='Actif').count()
        inactifs = Equipement.query.filter_by(statut='Inactif').count()
        en_maintenance = Equipement.query.filter_by(statut='En maintenance').count()
        
        # Statistiques par secteur
        secteurs_stats = db.session.query(
            Secteur.nom,
            db.func.count(Equipement.id).label('count')
        ).outerjoin(Equipement).group_by(Secteur.id, Secteur.nom).all()
        
        return jsonify({
            'total': total,
            'actifs': actifs,
            'inactifs': inactifs,
            'en_maintenance': en_maintenance,
            'par_secteur': [{'secteur': nom, 'count': count} for nom, count in secteurs_stats]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

