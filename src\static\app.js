// Configuration de l'API
const API_BASE = '/api';

// Variables globales
let secteurs = [];
let equipements = [];
let pieces = [];
let interventions = [];

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function() {
    loadDashboard();
});

// Navigation entre les sections
function showSection(sectionName) {
    // Masquer toutes les sections
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Retirer la classe active de tous les liens
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Afficher la section demandée
    document.getElementById(sectionName).style.display = 'block';
    
    // Ajouter la classe active au lien correspondant
    event.target.classList.add('active');
    
    // Charger les données selon la section
    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'secteurs':
            loadSecteurs();
            break;
        case 'equipements':
            loadEquipements();
            break;
        case 'pieces':
            loadPieces();
            break;
        case 'interventions':
            loadInterventions();
            break;
        case 'historique':
            loadHistorique();
            break;
    }
}

// Fonctions utilitaires
function formatDate(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR');
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('fr-FR');
}

function formatCurrency(amount) {
    if (!amount) return '-';
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount);
}

// Fonctions API
async function apiCall(endpoint, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(API_BASE + endpoint, options);
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Erreur API');
        }
        
        return await response.json();
    } catch (error) {
        console.error('Erreur API:', error);
        alert('Erreur: ' + error.message);
        throw error;
    }
}

// Dashboard
async function loadDashboard() {
    try {
        // Charger les statistiques
        const [equipStats, pieceStats, interventionStats] = await Promise.all([
            apiCall('/equipements/stats'),
            apiCall('/pieces/stats'),
            apiCall('/interventions/stats')
        ]);
        
        // Mettre à jour les cartes de statistiques
        document.getElementById('total-equipements').textContent = equipStats.total;
        document.getElementById('total-interventions').textContent = interventionStats.total;
        document.getElementById('total-pieces').textContent = pieceStats.total;
        document.getElementById('stock-critique').textContent = pieceStats.stock_critique;
        
        // Charger les interventions récentes
        const interventionsRecentes = await apiCall('/interventions?limit=5');
        displayInterventionsRecentes(interventionsRecentes);
        
        // Charger les pièces en stock critique
        const piecesCritiques = await apiCall('/pieces/stock-critique');
        displayPiecesCritiques(piecesCritiques);
        
    } catch (error) {
        console.error('Erreur lors du chargement du dashboard:', error);
    }
}

function displayInterventionsRecentes(interventions) {
    const container = document.getElementById('interventions-recentes');
    if (interventions.length === 0) {
        container.innerHTML = '<p class="text-muted">Aucune intervention récente</p>';
        return;
    }
    
    const html = interventions.map(intervention => `
        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
            <div>
                <strong>${intervention.titre}</strong><br>
                <small class="text-muted">${intervention.equipement_nom}</small>
            </div>
            <span class="badge bg-${getStatusColor(intervention.statut)}">${intervention.statut}</span>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function displayPiecesCritiques(pieces) {
    const container = document.getElementById('pieces-critiques');
    if (pieces.length === 0) {
        container.innerHTML = '<p class="text-muted">Aucune pièce en stock critique</p>';
        return;
    }
    
    const html = pieces.map(piece => `
        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
            <div>
                <strong>${piece.nom}</strong><br>
                <small class="text-muted">${piece.equipement_nom}</small>
            </div>
            <span class="stock-critique">${piece.quantite_stock}/${piece.quantite_min}</span>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function getStatusColor(statut) {
    switch(statut) {
        case 'Actif': case 'Terminée': return 'success';
        case 'En cours': case 'Planifiée': return 'warning';
        case 'Inactif': case 'Annulée': return 'danger';
        default: return 'secondary';
    }
}

// Secteurs
async function loadSecteurs() {
    try {
        secteurs = await apiCall('/secteurs');
        displaySecteurs();
    } catch (error) {
        console.error('Erreur lors du chargement des secteurs:', error);
    }
}

function displaySecteurs() {
    const tbody = document.getElementById('secteurs-table-body');
    if (secteurs.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center">Aucun secteur trouvé</td></tr>';
        return;
    }
    
    const html = secteurs.map(secteur => `
        <tr>
            <td>${secteur.nom}</td>
            <td>${secteur.numero || '-'}</td>
            <td>${secteur.description || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editSecteur(${secteur.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteSecteur(${secteur.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// Équipements
async function loadEquipements() {
    try {
        equipements = await apiCall('/equipements');
        displayEquipements();
    } catch (error) {
        console.error('Erreur lors du chargement des équipements:', error);
    }
}

function displayEquipements() {
    const tbody = document.getElementById('equipements-table-body');
    if (equipements.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun équipement trouvé</td></tr>';
        return;
    }
    
    const html = equipements.map(equipement => `
        <tr>
            <td>${equipement.nom}</td>
            <td>${equipement.code_equipement || '-'}</td>
            <td>${equipement.secteur_nom || '-'}</td>
            <td><span class="badge bg-${getStatusColor(equipement.statut)}">${equipement.statut}</span></td>
            <td>${equipement.fabricant || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editEquipement(${equipement.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteEquipement(${equipement.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// Pièces de rechange
async function loadPieces() {
    try {
        pieces = await apiCall('/pieces');
        displayPieces();
    } catch (error) {
        console.error('Erreur lors du chargement des pièces:', error);
    }
}

function displayPieces() {
    const tbody = document.getElementById('pieces-table-body');
    if (pieces.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">Aucune pièce trouvée</td></tr>';
        return;
    }
    
    const html = pieces.map(piece => `
        <tr ${piece.stock_critique ? 'class="table-warning"' : ''}>
            <td>${piece.nom}</td>
            <td>${piece.reference || '-'}</td>
            <td>${piece.equipement_nom || '-'}</td>
            <td ${piece.stock_critique ? 'class="stock-critique"' : ''}>${piece.quantite_stock}</td>
            <td>${piece.quantite_min}</td>
            <td>${formatCurrency(piece.prix_unitaire)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editPiece(${piece.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deletePiece(${piece.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// Interventions
async function loadInterventions() {
    try {
        interventions = await apiCall('/interventions');
        displayInterventions();
    } catch (error) {
        console.error('Erreur lors du chargement des interventions:', error);
    }
}

function displayInterventions() {
    const tbody = document.getElementById('interventions-table-body');
    if (interventions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">Aucune intervention trouvée</td></tr>';
        return;
    }
    
    const html = interventions.map(intervention => `
        <tr>
            <td>${intervention.titre}</td>
            <td><span class="badge bg-info">${intervention.type_intervention}</span></td>
            <td>${intervention.equipement_nom || '-'}</td>
            <td>${intervention.technicien || '-'}</td>
            <td>${formatDate(intervention.date_prevue)}</td>
            <td><span class="badge bg-${getStatusColor(intervention.statut)}">${intervention.statut}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editIntervention(${intervention.id})">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteIntervention(${intervention.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

// Historique
async function loadHistorique() {
    try {
        const historique = await apiCall('/historique?limit=50');
        displayHistorique(historique);
    } catch (error) {
        console.error('Erreur lors du chargement de l\'historique:', error);
    }
}

function displayHistorique(historique) {
    const tbody = document.getElementById('historique-table-body');
    if (historique.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">Aucun historique trouvé</td></tr>';
        return;
    }
    
    const html = historique.map(entry => `
        <tr>
            <td>${formatDateTime(entry.timestamp)}</td>
            <td><span class="badge bg-secondary">${entry.table_name}</span></td>
            <td><span class="badge bg-${getActionColor(entry.action)}">${entry.action}</span></td>
            <td>${entry.user_name || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="showHistoryDetails(${entry.id})">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
}

function getActionColor(action) {
    switch(action) {
        case 'CREATE': return 'success';
        case 'UPDATE': return 'warning';
        case 'DELETE': return 'danger';
        default: return 'secondary';
    }
}

// Fonctions de modal (à implémenter)
function showAddSecteurModal() {
    alert('Fonctionnalité à implémenter: Ajouter un secteur');
}

function showAddEquipementModal() {
    alert('Fonctionnalité à implémenter: Ajouter un équipement');
}

function showAddPieceModal() {
    alert('Fonctionnalité à implémenter: Ajouter une pièce');
}

function showAddInterventionModal() {
    alert('Fonctionnalité à implémenter: Ajouter une intervention');
}

function editSecteur(id) {
    alert('Fonctionnalité à implémenter: Modifier le secteur ' + id);
}

function deleteSecteur(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce secteur ?')) {
        alert('Fonctionnalité à implémenter: Supprimer le secteur ' + id);
    }
}

function editEquipement(id) {
    alert('Fonctionnalité à implémenter: Modifier l\'équipement ' + id);
}

function deleteEquipement(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet équipement ?')) {
        alert('Fonctionnalité à implémenter: Supprimer l\'équipement ' + id);
    }
}

function editPiece(id) {
    alert('Fonctionnalité à implémenter: Modifier la pièce ' + id);
}

function deletePiece(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette pièce ?')) {
        alert('Fonctionnalité à implémenter: Supprimer la pièce ' + id);
    }
}

function editIntervention(id) {
    alert('Fonctionnalité à implémenter: Modifier l\'intervention ' + id);
}

function deleteIntervention(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette intervention ?')) {
        alert('Fonctionnalité à implémenter: Supprimer l\'intervention ' + id);
    }
}

function showHistoryDetails(id) {
    alert('Fonctionnalité à implémenter: Afficher les détails de l\'historique ' + id);
}

