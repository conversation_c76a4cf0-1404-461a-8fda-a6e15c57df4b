from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from src.models.equipement import db, Historique

historique_bp = Blueprint('historique', __name__)

@historique_bp.route('/historique', methods=['GET'])
def get_historique():
    """Récupère l'historique avec filtres optionnels"""
    try:
        query = Historique.query
        
        # Filtres optionnels
        table_name = request.args.get('table_name')
        record_id = request.args.get('record_id', type=int)
        action = request.args.get('action')
        user_name = request.args.get('user_name')
        date_debut = request.args.get('date_debut')
        date_fin = request.args.get('date_fin')
        limit = request.args.get('limit', type=int, default=100)
        
        if table_name:
            query = query.filter(Historique.table_name == table_name)
        
        if record_id:
            query = query.filter(Historique.record_id == record_id)
        
        if action:
            query = query.filter(Historique.action == action)
        
        if user_name:
            query = query.filter(Historique.user_name.contains(user_name))
        
        if date_debut:
            query = query.filter(Historique.timestamp >= datetime.strptime(date_debut, '%Y-%m-%d'))
        
        if date_fin:
            query = query.filter(Historique.timestamp <= datetime.strptime(date_fin, '%Y-%m-%d'))
        
        # Trier par timestamp décroissant et limiter les résultats
        historique = query.order_by(Historique.timestamp.desc()).limit(limit).all()
        return jsonify([h.to_dict() for h in historique])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@historique_bp.route('/historique/<int:historique_id>', methods=['GET'])
def get_historique_entry(historique_id):
    """Récupère une entrée d'historique par son ID"""
    try:
        historique = Historique.query.get_or_404(historique_id)
        return jsonify(historique.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@historique_bp.route('/historique/record/<string:table_name>/<int:record_id>', methods=['GET'])
def get_record_history(table_name, record_id):
    """Récupère l'historique complet d'un enregistrement spécifique"""
    try:
        historique = Historique.query.filter_by(
            table_name=table_name,
            record_id=record_id
        ).order_by(Historique.timestamp.desc()).all()
        
        return jsonify([h.to_dict() for h in historique])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@historique_bp.route('/historique/stats', methods=['GET'])
def get_historique_stats():
    """Récupère les statistiques de l'historique"""
    try:
        # Statistiques générales
        total = Historique.query.count()
        
        # Actions par type
        creates = Historique.query.filter_by(action='CREATE').count()
        updates = Historique.query.filter_by(action='UPDATE').count()
        deletes = Historique.query.filter_by(action='DELETE').count()
        
        # Activité par table
        tables_stats = db.session.query(
            Historique.table_name,
            db.func.count(Historique.id).label('count')
        ).group_by(Historique.table_name).all()
        
        # Activité des dernières 24h
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_activity = Historique.query.filter(
            Historique.timestamp >= yesterday
        ).count()
        
        # Activité par jour des 7 derniers jours
        week_ago = datetime.utcnow() - timedelta(days=7)
        daily_activity = db.session.query(
            db.func.date(Historique.timestamp).label('date'),
            db.func.count(Historique.id).label('count')
        ).filter(
            Historique.timestamp >= week_ago
        ).group_by(
            db.func.date(Historique.timestamp)
        ).order_by('date').all()
        
        return jsonify({
            'total': total,
            'creates': creates,
            'updates': updates,
            'deletes': deletes,
            'recent_activity': recent_activity,
            'par_table': [{'table': table, 'count': count} for table, count in tables_stats],
            'activite_quotidienne': [{'date': str(date), 'count': count} for date, count in daily_activity]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@historique_bp.route('/historique/recent', methods=['GET'])
def get_recent_activity():
    """Récupère l'activité récente (dernières 24h)"""
    try:
        yesterday = datetime.utcnow() - timedelta(days=1)
        limit = request.args.get('limit', type=int, default=50)
        
        historique = Historique.query.filter(
            Historique.timestamp >= yesterday
        ).order_by(Historique.timestamp.desc()).limit(limit).all()
        
        return jsonify([h.to_dict() for h in historique])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@historique_bp.route('/historique/cleanup', methods=['POST'])
def cleanup_old_history():
    """Nettoie l'historique ancien (plus de 1 an)"""
    try:
        data = request.get_json()
        days = data.get('days', 365)  # Par défaut, supprimer les entrées de plus d'un an
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Compter les entrées à supprimer
        count = Historique.query.filter(Historique.timestamp < cutoff_date).count()
        
        # Supprimer les entrées anciennes
        Historique.query.filter(Historique.timestamp < cutoff_date).delete()
        db.session.commit()
        
        return jsonify({
            'message': f'{count} entrées d\'historique supprimées',
            'deleted_count': count,
            'cutoff_date': cutoff_date.isoformat()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

