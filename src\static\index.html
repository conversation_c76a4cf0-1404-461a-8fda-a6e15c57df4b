<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion d'Équipements</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .nav-link.active {
            background-color: #0d6efd;
            color: white !important;
        }
        .card-stat {
            border-left: 4px solid #0d6efd;
        }
        .stock-critique {
            color: #dc3545;
            font-weight: bold;
        }
        .table-responsive {
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-center mb-4">Gestion d'Équipements</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                                <i class="bi bi-speedometer2"></i> Tableau de bord
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('secteurs')">
                                <i class="bi bi-building"></i> Secteurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('equipements')">
                                <i class="bi bi-gear"></i> Équipements
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('pieces')">
                                <i class="bi bi-tools"></i> Pièces de rechange
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('interventions')">
                                <i class="bi bi-wrench"></i> Interventions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('historique')">
                                <i class="bi bi-clock-history"></i> Historique
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Dashboard Section -->
                <div id="dashboard" class="section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Tableau de bord</h1>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card card-stat">
                                <div class="card-body">
                                    <h5 class="card-title">Équipements</h5>
                                    <h3 class="text-primary" id="total-equipements">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card card-stat">
                                <div class="card-body">
                                    <h5 class="card-title">Interventions</h5>
                                    <h3 class="text-success" id="total-interventions">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card card-stat">
                                <div class="card-body">
                                    <h5 class="card-title">Pièces</h5>
                                    <h3 class="text-info" id="total-pieces">-</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card card-stat">
                                <div class="card-body">
                                    <h5 class="card-title">Stock critique</h5>
                                    <h3 class="text-danger" id="stock-critique">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Interventions récentes</h5>
                                </div>
                                <div class="card-body">
                                    <div id="interventions-recentes"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Pièces en stock critique</h5>
                                </div>
                                <div class="card-body">
                                    <div id="pieces-critiques"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Secteurs Section -->
                <div id="secteurs" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Secteurs</h1>
                        <button class="btn btn-primary" onclick="showAddSecteurModal()">
                            <i class="bi bi-plus"></i> Ajouter un secteur
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Numéro</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="secteurs-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Équipements Section -->
                <div id="equipements" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Équipements</h1>
                        <button class="btn btn-primary" onclick="showAddEquipementModal()">
                            <i class="bi bi-plus"></i> Ajouter un équipement
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Code</th>
                                    <th>Secteur</th>
                                    <th>Statut</th>
                                    <th>Fabricant</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="equipements-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pièces Section -->
                <div id="pieces" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Pièces de rechange</h1>
                        <button class="btn btn-primary" onclick="showAddPieceModal()">
                            <i class="bi bi-plus"></i> Ajouter une pièce
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Référence</th>
                                    <th>Équipement</th>
                                    <th>Stock</th>
                                    <th>Stock min</th>
                                    <th>Prix unitaire</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="pieces-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Interventions Section -->
                <div id="interventions" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Interventions</h1>
                        <button class="btn btn-primary" onclick="showAddInterventionModal()">
                            <i class="bi bi-plus"></i> Ajouter une intervention
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Type</th>
                                    <th>Équipement</th>
                                    <th>Technicien</th>
                                    <th>Date prévue</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="interventions-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Historique Section -->
                <div id="historique" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Historique</h1>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Table</th>
                                    <th>Action</th>
                                    <th>Utilisateur</th>
                                    <th>Détails</th>
                                </tr>
                            </thead>
                            <tbody id="historique-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modals-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>

