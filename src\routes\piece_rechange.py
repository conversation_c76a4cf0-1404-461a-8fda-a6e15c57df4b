from flask import Blueprint, request, jsonify
from datetime import datetime
from src.models.equipement import db, PieceRechange, Equipement, Historique

piece_bp = Blueprint('piece_rechange', __name__)

@piece_bp.route('/pieces', methods=['GET'])
def get_pieces():
    """Récupère toutes les pièces de rechange avec filtres optionnels"""
    try:
        query = PieceRechange.query
        
        # Filtres optionnels
        equipement_id = request.args.get('equipement_id', type=int)
        stock_critique = request.args.get('stock_critique', type=bool)
        search = request.args.get('search')
        
        if equipement_id:
            query = query.filter(PieceRechange.equipement_id == equipement_id)
        
        if stock_critique:
            query = query.filter(PieceRechange.quantite_stock <= PieceRechange.quantite_min)
        
        if search:
            query = query.filter(
                db.or_(
                    PieceRechange.nom.contains(search),
                    PieceRechange.reference.contains(search),
                    PieceRechange.fournisseur.contains(search)
                )
            )
        
        pieces = query.all()
        return jsonify([piece.to_dict() for piece in pieces])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces/<int:piece_id>', methods=['GET'])
def get_piece(piece_id):
    """Récupère une pièce de rechange par son ID"""
    try:
        piece = PieceRechange.query.get_or_404(piece_id)
        return jsonify(piece.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces', methods=['POST'])
def create_piece():
    """Crée une nouvelle pièce de rechange"""
    try:
        data = request.get_json()
        
        # Validation des données requises
        if not data.get('nom'):
            return jsonify({'error': 'Le nom de la pièce est requis'}), 400
        
        if not data.get('equipement_id'):
            return jsonify({'error': 'L\'ID de l\'équipement est requis'}), 400
        
        # Vérifier que l'équipement existe
        equipement = Equipement.query.get(data['equipement_id'])
        if not equipement:
            return jsonify({'error': 'Équipement non trouvé'}), 400
        
        piece = PieceRechange(
            equipement_id=data['equipement_id'],
            nom=data['nom'],
            reference=data.get('reference'),
            description=data.get('description'),
            quantite_stock=data.get('quantite_stock', 0),
            quantite_min=data.get('quantite_min', 0),
            prix_unitaire=data.get('prix_unitaire'),
            fournisseur=data.get('fournisseur'),
            delai_livraison=data.get('delai_livraison')
        )
        
        db.session.add(piece)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('pieces_rechange', piece.id, 'CREATE', new_values=piece.to_dict())
        
        return jsonify(piece.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces/<int:piece_id>', methods=['PUT'])
def update_piece(piece_id):
    """Met à jour une pièce de rechange"""
    try:
        piece = PieceRechange.query.get_or_404(piece_id)
        data = request.get_json()
        
        # Sauvegarder les anciennes valeurs pour l'historique
        old_values = piece.to_dict()
        
        # Vérifier que l'équipement existe si modifié
        if data.get('equipement_id') and data['equipement_id'] != piece.equipement_id:
            equipement = Equipement.query.get(data['equipement_id'])
            if not equipement:
                return jsonify({'error': 'Équipement non trouvé'}), 400
            piece.equipement_id = data['equipement_id']
        
        # Mettre à jour les autres champs
        if 'nom' in data:
            piece.nom = data['nom']
        if 'reference' in data:
            piece.reference = data['reference']
        if 'description' in data:
            piece.description = data['description']
        if 'quantite_stock' in data:
            piece.quantite_stock = data['quantite_stock']
        if 'quantite_min' in data:
            piece.quantite_min = data['quantite_min']
        if 'prix_unitaire' in data:
            piece.prix_unitaire = data['prix_unitaire']
        if 'fournisseur' in data:
            piece.fournisseur = data['fournisseur']
        if 'delai_livraison' in data:
            piece.delai_livraison = data['delai_livraison']
        
        piece.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('pieces_rechange', piece.id, 'UPDATE', old_values=old_values, new_values=piece.to_dict())
        
        return jsonify(piece.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces/<int:piece_id>', methods=['DELETE'])
def delete_piece(piece_id):
    """Supprime une pièce de rechange"""
    try:
        piece = PieceRechange.query.get_or_404(piece_id)
        
        # Vérifier s'il y a des utilisations dans les interventions
        if piece.pieces_utilisees:
            return jsonify({'error': 'Impossible de supprimer une pièce utilisée dans des interventions'}), 400
        
        # Sauvegarder les valeurs pour l'historique
        old_values = piece.to_dict()
        
        db.session.delete(piece)
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('pieces_rechange', piece_id, 'DELETE', old_values=old_values)
        
        return jsonify({'message': 'Pièce de rechange supprimée avec succès'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces/<int:piece_id>/stock', methods=['PUT'])
def update_stock(piece_id):
    """Met à jour le stock d'une pièce de rechange"""
    try:
        piece = PieceRechange.query.get_or_404(piece_id)
        data = request.get_json()
        
        if 'quantite' not in data:
            return jsonify({'error': 'La quantité est requise'}), 400
        
        # Sauvegarder les anciennes valeurs pour l'historique
        old_values = piece.to_dict()
        
        piece.quantite_stock = data['quantite']
        piece.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Enregistrer dans l'historique
        Historique.log_action('pieces_rechange', piece.id, 'UPDATE', old_values=old_values, new_values=piece.to_dict())
        
        return jsonify(piece.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces/stock-critique', methods=['GET'])
def get_stock_critique():
    """Récupère les pièces en stock critique"""
    try:
        pieces = PieceRechange.query.filter(
            PieceRechange.quantite_stock <= PieceRechange.quantite_min
        ).all()
        return jsonify([piece.to_dict() for piece in pieces])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@piece_bp.route('/pieces/stats', methods=['GET'])
def get_pieces_stats():
    """Récupère les statistiques des pièces de rechange"""
    try:
        total = PieceRechange.query.count()
        stock_critique = PieceRechange.query.filter(
            PieceRechange.quantite_stock <= PieceRechange.quantite_min
        ).count()
        
        # Valeur totale du stock
        valeur_stock = db.session.query(
            db.func.sum(PieceRechange.quantite_stock * PieceRechange.prix_unitaire)
        ).scalar() or 0
        
        return jsonify({
            'total': total,
            'stock_critique': stock_critique,
            'valeur_stock': float(valeur_stock)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

